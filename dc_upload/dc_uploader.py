#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DC文件上传工具
实现图片和任务上传功能
"""

import os
import requests
import mimetypes
import json
import asyncio
from pathlib import Path
from playwright.async_api import async_playwright


class DCUploader:
    """DC文件上传器"""

    def __init__(self, authorization_token):
        """
        初始化上传器
        
        Args:
            authorization_token: JWT认证令牌
        """
        self.base_url = "https://api.dc.zentaotech.com"
        self.headers = {
            "authorization": authorization_token,
            "client": "admin_login_scene:",
            "accept": "application/json, text/plain, */*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "dnt": "1",
            "origin": "https://demo.dc.zentaotech.com",
            "referer": "https://demo.dc.zentaotech.com/",
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"macOS"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-site",
            "priority": "u=1, i"
        }
    
    def upload_image(self, file_path):
        """
        上传图片文件
        
        Args:
            file_path: 本地图片文件路径
            
        Returns:
            str: 上传成功返回文件URL，失败返回None
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件类型不支持
        """
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 获取文件信息
        file_path = Path(file_path)
        file_name = file_path.name
        
        # 检查是否为图片文件
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if not mime_type or not mime_type.startswith('image/'):
            raise ValueError(f"不支持的文件类型: {mime_type}，仅支持图片文件")
        
        # 准备上传URL
        upload_url = f"{self.base_url}/adminapi/file/upload"
        
        try:
            # 准备文件数据
            with open(file_path, 'rb') as file:
                files = {
                    'file': (file_name, file, mime_type)
                }
                
                # 发送POST请求
                response = requests.post(
                    upload_url,
                    headers=self.headers,
                    files=files,
                    timeout=30
                )
                
                # 检查响应状态
                response.raise_for_status()

                # 解析响应并返回fileUrl
                if response.content:
                    response_data = response.json()
                    if response_data.get('code') == 200 and 'data' in response_data:
                        return response_data['data'].get('fileUrl')

                return None
                
        except requests.exceptions.RequestException as e:
            print(f"上传失败: {str(e)}")
            return None
        except Exception as e:
            print(f"未知错误: {str(e)}")
            return None
    
    def upload_quest(self, quest_data):
        """
        上传题目数据

        Args:
            quest_data: 题目数据字典，包含编号、学科、题型等信息

        Returns:
            bool: 上传成功返回True，失败返回False
        """
        return asyncio.run(self._upload_quest_async(quest_data))

    async def _upload_quest_async(self, quest_data):
        """异步上传题目数据"""
        try:
            async with async_playwright() as p:
                # 启动Chrome浏览器
                browser = await p.chromium.launch(
                    headless=False,
                    channel="chrome"  # 使用系统安装的Chrome
                )
                context = await browser.new_context()

                # 注入authorization到localStorage
                page = await context.new_page()
                await page.goto("https://demo.dc.zentaotech.com")

                # 注入token到localStorage
                await page.evaluate(f"""
                    localStorage.setItem('Authorization', '{self.headers["authorization"]}');
                    localStorage.setItem('Client', 'admin_login_scene:');
                """)

                # 导航到题目添加页面
                await page.goto("https://demo.dc.zentaotech.com/question/add_question")
                await page.wait_for_load_state('networkidle')

                # 等待页面完全加载
                await page.wait_for_timeout(2000)

                # 填写编号
                if "编号" in quest_data:
                    try:
                        await page.wait_for_selector('input[placeholder="请输入编号"]', timeout=10000)
                        await page.fill('input[placeholder="请输入编号"]', quest_data["编号"])
                        print(f"已填写编号: {quest_data['编号']}")
                    except Exception as e:
                        print(f"填写编号失败: {str(e)}")

                # 选择学科
                if "学科" in quest_data:
                    print(f"正在选择学科: {quest_data['学科']}")
                    await self._select_radio_option(page, quest_data["学科"])

                # 选择题型
                if "题型" in quest_data:
                    print(f"正在选择题型: {quest_data['题型']}")
                    await self._select_radio_option(page, quest_data["题型"])

                # 选择科目
                if "科目" in quest_data:
                    print(f"正在选择科目: {quest_data['科目']}")
                    await self._select_radio_option(page, quest_data["科目"])

                # 选择来源
                if "来源" in quest_data and quest_data["来源"]:
                    await self._select_source(page, quest_data["来源"])

                # 选择年份
                # if "年份" in quest_data:
                await self._select_year(page, str(2025))

                # 填写题目
                if "题干" in quest_data:
                    await self._fill_content_field(page, "题目", quest_data["题干"])

                # 填写分析点评
                if "分析点评" in quest_data:
                    await self._fill_content_field(page, "分析点评", quest_data["分析点评"])

                # 填写文字解答
                if "文字解答" in quest_data:
                    await self._fill_content_field(page, "文字解答", quest_data["文字解答"])

                # 处理选项（单选题）
                if "选项" in quest_data and quest_data["选项"]:
                    await self._fill_options(page, quest_data["选项"])

                # 填写答案
                if "答案" in quest_data:
                    # 检查是否是单选题
                    if "题型" in quest_data and quest_data["题型"] == "单选题":
                        await self._select_answer_option(page, quest_data["答案"])
                    else:
                        await self._fill_content_field(page, "答案", quest_data["答案"])

                # 点击提交按钮
                print("题目信息已填写完成，正在提交...")
                submit_success = await self._click_submit_button(page)

                if submit_success:
                    print("题目提交成功！")
                    await page.wait_for_timeout(3000)  # 等待3秒查看结果
                else:
                    print("未找到提交按钮，请手动提交")
                    await page.wait_for_timeout(5000)  # 等待5秒让用户手动操作

                await browser.close()
                return submit_success

        except Exception as e:
            print(f"上传题目失败: {str(e)}")
            return False

    async def _select_radio_option(self, page, text):
        """选择单选按钮选项"""
        try:
            # 查找所有.el-radio__label元素
            labels = await page.query_selector_all('.el-radio__label')
            for label in labels:
                label_text = await label.text_content()
                if label_text and label_text.strip() == text:
                    await label.click()
                    await page.wait_for_timeout(500)  # 等待选择生效
                    return True
            print(f"未找到选项: {text}")
            return False
        except Exception as e:
            print(f"选择选项失败 {text}: {str(e)}")
            return False

    async def _select_source(self, page, source_list):
        """选择来源（级联选择器）"""
        try:
            # 查找来源标签并点击
            source_label = await page.query_selector('label:has-text("来源")')
            if source_label:
                await source_label.click()
                await page.wait_for_timeout(1000)  # 等待弹窗出现

                # 按照source_list的顺序依次点击
                for source_item in source_list:
                    # 重新查找.el-cascader-node元素
                    nodes = await page.query_selector_all('.el-cascader-node')
                    found = False
                    for node in nodes:
                        node_text = await node.text_content()
                        if node_text and node_text.strip() == source_item:
                            await node.click()
                            await page.wait_for_timeout(500)  # 等待下一级加载
                            found = True
                            break

                    if not found:
                        print(f"未找到来源选项: {source_item}")
                        return False

                return True
            else:
                print("未找到来源标签")
                return False
        except Exception as e:
            print(f"选择来源失败: {str(e)}")
            return False


    async def _select_year(self, page, year=2025):
        """选择年份"""
        try:
            # 查找年份标签并点击
            year_label = await page.query_selector('label:has-text("年份")')
            if year_label:
                await year_label.click()
                await page.wait_for_timeout(1000)  # 等待下拉框出现

                # 查找年份选项
                items = await page.query_selector_all('.el-select-dropdown__item')
                for item in items:
                    item_text = await item.text_content()
                    if item_text and item_text.strip() == year:
                        await item.click()
                        await page.wait_for_timeout(500)
                        return True

                print(f"未找到年份选项: {year}")
                return False
            else:
                print("未找到年份标签")
                return False
        except Exception as e:
            print(f"选择年份失败: {str(e)}")
            return False

    async def _fill_content_field(self, page, field_name, content):
        """填写内容字段（题目、分析点评、文字解答、答案）"""
        try:
            # 查找对应的标签
            label = await page.query_selector(f'.el-form-item__label:has-text("{field_name}")')
            if label:
                # 获取标签的父元素
                parent = await label.query_selector('xpath=..')
                if parent:
                    # 在父元素下查找role="textbox"的元素
                    textbox = await parent.query_selector('[role="textbox"]')
                    if textbox:
                        # 先找到父元素下面的data-cke-tooltip-text="源"的元素并点击
                        source_button = await parent.query_selector('[data-cke-tooltip-text="源"]')
                        if source_button:
                            await source_button.click()
                            await page.wait_for_timeout(500)  # 等待切换到源模式
                            print(f"已点击{field_name}的源按钮")

                        # 然后点击父元素里的textarea元素并输入
                        textarea = await parent.query_selector('textarea')
                        if textarea:
                            await textarea.click()
                            await page.keyboard.press('Control+a')  # 全选
                            await page.keyboard.type(content)
                            await page.wait_for_timeout(500)
                            print(f"已填写{field_name}: {content[:50]}...")
                            return True
                        else:
                            # 如果没有textarea，尝试原来的方式
                            await textbox.click()
                            await page.keyboard.press('Control+a')  # 全选
                            await page.keyboard.type(content)
                            await page.wait_for_timeout(500)
                            print(f"已填写{field_name}: {content[:50]}...")
                            return True
                    else:
                        print(f"未找到{field_name}的输入框")
                        return False
                else:
                    print(f"未找到{field_name}标签的父元素")
                    return False
            else:
                print(f"未找到{field_name}标签")
                return False
        except Exception as e:
            print(f"填写{field_name}失败: {str(e)}")
            return False

    async def _fill_options(self, page, options):
        """填写选项（单选题）"""
        try:
            for option in options:
                choice = option.get("choice", "")
                context = option.get("context", "")

                if not choice or not context:
                    continue

                # 查找对应的标签
                label = await page.query_selector(f'.el-form-item__label:has-text("{choice}")')
                if label:
                    # 获取标签的父元素
                    parent = await label.query_selector('xpath=..')
                    if parent:
                        # 在父元素下查找role="textbox"的元素
                        textbox = await parent.query_selector('[role="textbox"]')
                        if textbox:
                            # 先找到父元素下面的data-cke-tooltip-text="源"的元素并点击
                            source_button = await parent.query_selector('[data-cke-tooltip-text="源"]')
                            if source_button:
                                await source_button.click()
                                await page.wait_for_timeout(500)  # 等待切换到源模式
                                print(f"已点击选项 {choice} 的源按钮")

                            # 然后点击父元素里的textarea元素并输入
                            textarea = await parent.query_selector('textarea')
                            if textarea:
                                await textarea.click()
                                await page.keyboard.press('Control+a')  # 全选
                                await page.keyboard.type(context)
                                await page.wait_for_timeout(500)
                                print(f"已填写选项 {choice}: {context}")
                            else:
                                # 如果没有textarea，尝试原来的方式
                                await textbox.click()
                                await page.keyboard.press('Control+a')  # 全选
                                await page.keyboard.type(context)
                                await page.wait_for_timeout(500)
                                print(f"已填写选项 {choice}: {context}")
                        else:
                            print(f"未找到选项 {choice} 的输入框")
                    else:
                        print(f"未找到选项 {choice} 标签的父元素")
                else:
                    print(f"未找到选项标签: {choice}")

            return True
        except Exception as e:
            print(f"填写选项失败: {str(e)}")
            return False

    async def _select_answer_option(self, page, answer):
        """选择答案选项（单选题）"""
        try:
            # 查找所有.el-radio__label元素
            labels = await page.query_selector_all('.el-radio__label')
            for label in labels:
                label_text = await label.text_content()
                if label_text and label_text.strip() == answer:
                    await label.click()
                    await page.wait_for_timeout(500)  # 等待选择生效
                    print(f"已选择答案: {answer}")
                    return True

            print(f"未找到答案选项: {answer}")
            return False
        except Exception as e:
            print(f"选择答案选项失败: {str(e)}")
            return False

    async def _click_submit_button(self, page):
        """点击提交按钮"""
        try:
            # 查找textContent为"提交"的button元素
            submit_button = await page.query_selector('button:has-text("提交")')
            if submit_button:
                await submit_button.click()
                await page.wait_for_timeout(1000)  # 等待提交处理
                return True
            else:
                # 如果没找到，尝试其他可能的选择器
                buttons = await page.query_selector_all('button')
                for button in buttons:
                    button_text = await button.text_content()
                    if button_text and button_text.strip() == "提交":
                        await button.click()
                        await page.wait_for_timeout(1000)
                        return True

                print("未找到提交按钮")
                return False
        except Exception as e:
            print(f"点击提交按钮失败: {str(e)}")
            return False


def main():
    """测试函数"""
    # 示例JWT令牌（请替换为实际的令牌）
    token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTM2ODg0OTksIm5iZiI6MTc1MzY4ODQ5OSwiZXhwIjoxNzUzNzc0ODk5LCJkYXRhIjp7InVzZXJfaWQiOjIxLCJ1c2VyX25hbWUiOiJzaGl0aWx1cnUifX0.eqNfsxs9iN0khPdFsvptWkbOjCzuJ1WitozdDy6f6yY"

    # 创建上传器实例
    uploader = DCUploader(token)

    # 测试图片上传
    # image_path = "/Users/<USER>/Downloads/a.jpg"
    # result = uploader.upload_image(image_path)
    # print(f"图片上传结果: {result}")

    # 测试题目上传
    # 读取demo.json文件
    try:
        with open("/Users/<USER>/Downloads/book_01/html_vis/李-880题数二_高等数学 第二章 一元函数微分学及其应用_10-1_57693.json", "r", encoding="utf-8") as f:
            quest_data = json.load(f)

        print("开始上传题目...")
        result = uploader.upload_quest(quest_data)
        print(f"题目上传结果: {result}")

    except FileNotFoundError:
        print("未找到demo.json文件")
    except Exception as e:
        print(f"读取demo.json失败: {str(e)}")


if __name__ == "__main__":
    main()
