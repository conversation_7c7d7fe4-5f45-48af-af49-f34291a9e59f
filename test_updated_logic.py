#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的输入逻辑
"""

import json
from dc_uploader import DCUploader


def test_updated_input_logic():
    """测试更新后的输入逻辑"""
    # 示例JWT令牌（请替换为实际的令牌）
    token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTM0MzY3NzQsIm5iZiI6MTc1MzQzNjc3NCwiZXhwIjoxNzUzNTIzMTc0LCJkYXRhIjp7InVzZXJfaWQiOjIxLCJ1c2VyX25hbWUiOiJzaGl0aWx1cnUifX0.8yo8POhmXCIRqFSLIq3AF0yazAS66QM4aQgFQqknW-I"

    # 创建上传器实例
    uploader = DCUploader(token)

    # 创建测试数据
    test_data = {
        "编号": "测试-更新逻辑-001",
        "学科": "数学",
        "题型": "单选题",
        "科目": "数一",
        "来源": ["线性代数-日练习", "行列式", "基础"],
        "年份": 2024,
        "题干": "下列关于矩阵特征值的说法正确的是（）",
        "选项": [
            {
                "choice": "A",
                "context": "矩阵的特征值一定是实数"
            },
            {
                "choice": "B", 
                "context": "矩阵的特征值可能是复数"
            },
            {
                "choice": "C",
                "context": "矩阵的特征值一定大于零"
            },
            {
                "choice": "D",
                "context": "矩阵的特征值一定小于零"
            }
        ],
        "分析点评": "本题考查矩阵特征值的基本性质。矩阵的特征值可能是实数也可能是复数，这取决于矩阵的具体形式。",
        "文字解答": "矩阵的特征值是特征方程的根，而特征方程是一个多项式方程，其根可能是实数也可能是复数。因此选项B正确。",
        "答案": "B"
    }

    print("开始测试更新后的输入逻辑...")
    print(f"题型: {test_data['题型']}")
    print(f"选项数量: {len(test_data['选项'])}")
    print(f"答案: {test_data['答案']}")
    
    try:
        result = uploader.upload_quest(test_data)
        print(f"测试结果: {result}")
    except Exception as e:
        print(f"测试失败: {str(e)}")


if __name__ == "__main__":
    test_updated_input_logic()
